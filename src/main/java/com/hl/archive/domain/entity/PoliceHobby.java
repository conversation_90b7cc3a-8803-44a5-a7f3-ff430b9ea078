package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 爱好表
 */
@ApiModel(description="爱好表")
@Data
@TableName(value = "police_hobby")
public class PoliceHobby {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value="身份证")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long idCard;

    /**
     * 兴趣名称
     */
    @TableField(value = "hobby_name")
    @ApiModelProperty(value="兴趣名称")
    private String hobbyName;

    /**
     * 兴趣类别
     */
    @TableField(value = "hobby_type")
    @ApiModelProperty(value="兴趣类别")
    private String hobbyType;

    /**
     * 字典值
     */
    @TableField(value = "hobby_code")
    @ApiModelProperty(value="字典值")
    private String hobbyCode;

    /**
     * 描述
     */
    @TableField(value = "description")
    @ApiModelProperty(value="描述")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value="创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value="更新人")
    private String updatedBy;

    /**
     * 删除标识
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value="删除标识")
    private Byte isDeleted;
}