package com.hl.archive.utils;

import cn.hutool.core.map.BiMap;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.security.UserUtils;
import com.hl.security.config.sso.cache.SsoCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class SsoCacheUtil {

    @SuppressWarnings("unchecked")
    public static Map<String, Object> getCacheMap(String cacheKey) {
        JSONObject cacheData = SsoCache.me.cacheData;
        Object user = cacheData.get(cacheKey);
        return (Map<String, Object>) user;
    }

    /**
     * 获取用户对象信息
     *
     * @param idCard 用户身份证
     * @return 用户对象
     */
    public static Object getUserObjByIdCard(String idCard) {
        return getCacheMap(UserCacheKey.USER).get(idCard);
    }

    public static String getUserOrgIdByIdCard(String idCard) {
        Object o = getCacheMap(UserCacheKey.USER).get(idCard);
        if (o != null) {
            JSONObject from = JSONObject.from(o);
            try {
                String string = from.getByPath("organization[0].organization_id").toString();
                return string;
            } catch (Exception e) {
                log.error("获取用户组织失败");
            }
        }
        return null;
    }



    public static Object getUserObjByPoliceId(String policeId) {
        return getCacheMap(UserCacheKey.POLICE).get(policeId);
    }

    public static String getUserOrgIdByPoliceId(String policeId) {
        Object o = getCacheMap(UserCacheKey.POLICE).get(policeId);
        if (o != null) {
            JSONObject from = JSONObject.from(o);
            try {
                String string = from.getByPath("organization[0].organization_id").toString();
                return string;
            } catch (Exception e) {
                log.error("获取用户组织失败");
            }
        }
        return null;
    }


    public static Object getOrganizationMap(String orgId) {
        return getCacheMap(UserCacheKey.ORGANIZATION).get(orgId);
    }

    public static String getOrganizationIdByName(String organizationName) {
        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.ORGANIZATION);
        for (Map.Entry<String, Object> entry : cacheMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            JSONObject from = JSONObject.from(value);
            String organization_name = from.getString("organization_name");
            if (organizationName.equals(organization_name)) {
                return key;
            }
        }
        return null;
    }

    public static String getOrganizationIdByNameWithLike(String organizationName) {
        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.ORGANIZATION);
        for (Map.Entry<String, Object> entry : cacheMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            JSONObject from = JSONObject.from(value);
            String organization_name = from.getString("organization_name");
            if (organization_name.contains(organizationName)) {
                return key;
            }
        }
        return null;
    }


    /**
     * 根据 姓名和单位 检索 信息
     * @param name 姓名
     * @param organizationId 单位
     * @return 检索数据
     */
    public static List<JSONObject> getIdCardByNameAndOrganizationName(String name, String organizationId) {
        List<JSONObject> userList = new ArrayList<>();
        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.USER);
        for (Map.Entry<String, Object> objectEntry : cacheMap.entrySet()) {
            JSONObject from = JSONObject.from(objectEntry.getValue());
            try {
                String userName = from.getByPath("name").toString();

                String orgId = from.getByPath("organization[0].organization_id").toString();
                if (name.equals(userName) && organizationId.equals(orgId)) {
                    userList.add(from);
                }
            } catch (Exception e) {
                log.error("数据有误:{}", from);
            }
        }
        return userList;

    }

    public static String getOrganizationName(String orgId) {
        try {
            if ("67852323".equals(orgId)) {
                return "从严办";
            } else if ("74766765".equals(orgId)) {
                return "武进分局案审大队";
            } else if ("33276091".equals(orgId)) {
                return "维稳专班";
            }
            Object object = getCacheMap(UserCacheKey.ORGANIZATION).get(orgId);
            if (object != null) {
                JSONObject json = JSONObject.from(object);
                return json.getString("organization_name");
            } else {
                return orgId;
            }
        } catch (Exception e) {
            log.error("获取缓存数据失败", e);
            return orgId; // 返回默认值
        }
    }


    public static List<JSONObject> getPcsList() {
        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.ORGANIZATION);

        List<JSONObject> pcsList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : cacheMap.entrySet()) {
            JSONObject frommed = JSONObject.from(entry.getValue());
            if ("3".equals(frommed.getString("dwcj"))) {
                String organizationName = frommed.getString("organization_name");
                if (organizationName.contains("水上") || organizationName.contains("科教城")) {
                    continue;
                }
                frommed.put("type", getPcsType(frommed));
                pcsList.add(frommed);
            }
        }
        return pcsList;
    }


    public static List<JSONObject> getZrqListByPcsId(String organizationId) {
        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.ORGANIZATION);

        List<JSONObject> zrqList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : cacheMap.entrySet()) {
            JSONObject frommed = JSONObject.from(entry.getValue());
            if ("4".equals(frommed.getString("dwcj")) && frommed.getString("organization_id").startsWith(organizationId.substring(0, 8))) {
                zrqList.add(frommed);
            }
        }
        return zrqList;
    }


    public static List<JSONObject> getPcsListAndKj() {
        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.ORGANIZATION);

        List<JSONObject> pcsList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : cacheMap.entrySet()) {
            JSONObject frommed = JSONObject.from(entry.getValue());
            if ("3".equals(frommed.getString("dwcj"))) {
                String organizationName = frommed.getString("organization_name");
                if (organizationName.contains("水上")) {
                    continue;
                }
                frommed.put("type", getPcsType(frommed));
                pcsList.add(frommed);
            }
        }
        return pcsList;
    }


    public static List<String> getRoleUser(String role) {
        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.ROLE_USER);
        Object o = cacheMap.get(role);
        if (o != null) {
            return new ArrayList<>(JSONObject.from(o).keySet());
        }
        return null;
    }

    public static String getUserNameByIdCard(String idCard) {
        Object o = getCacheMap(UserCacheKey.USER).get(idCard);
        if (o != null) {
            JSONObject jsonObject = JSONObject.from(o);
            return jsonObject.getString("name");
        }
        return null;
    }


    public static String getUserNameWithList(Object value) {
        if (value == null || StringUtils.isBlank(value.toString())) {
            return "";
        }

        List<String> name = new ArrayList<>();


        List<String> list = JSONArray.parseArray(value.toString(), String.class);

        Map<String, Object> cacheMap = getCacheMap(UserCacheKey.USER);
        for (String s : list) {
            Object o = cacheMap.get(s);
            if (o != null) {
                JSONObject jsonObject = JSONObject.from(o);
                name.add(jsonObject.getString("name"));
            }
        }
        return String.join(",", name);
    }


    public static String getUserIdCard() {
        try {
            return UserUtils.getUser().getIdCard();
        } catch (Exception e) {
            log.error("获取用户身份证失败", e);
            return "";
        }
    }


    public static String getPcsType(JSONObject jsonObject) {
        String organizationName = jsonObject.getString("organization_name");

        if (organizationName.contains("湖塘派出所") ||
                organizationName.contains("鸣凰") ||
                organizationName.contains("南夏墅")) {
            return "A类";
        } else if (organizationName.contains("马杭") ||
                organizationName.contains("牛塘") ||
                organizationName.contains("新城") ||
                organizationName.contains("礼嘉")
        ) {
            return "B类";
        } else if (organizationName.contains("洛阳") ||
                organizationName.contains("滆湖") ||
                organizationName.contains("嘉泽") ||
                organizationName.contains("前黄")) {
            return "C类";
        } else if (organizationName.contains("湟里") ||
                organizationName.contains("漕桥") ||
                organizationName.contains("潘家") ||
                organizationName.contains("雪堰") ||
                organizationName.contains("寨桥")
        ) {
            return "E类";
        } else if (organizationName.contains("东安")) {
            return "F类";
        } else {
            return "";
        }

    }


}
