<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceHobbyMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceHobby">
    <!--@mbg.generated-->
    <!--@Table police_hobby-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="BIGINT" property="idCard" />
    <result column="hobby_name" jdbcType="VARCHAR" property="hobbyName" />
    <result column="hobby_type" jdbcType="VARCHAR" property="hobbyType" />
    <result column="hobby_code" jdbcType="VARCHAR" property="hobbyCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, hobby_name, hobby_type, hobby_code, description, created_at, updated_at, 
    created_by, updated_by, is_deleted
  </sql>
</mapper>