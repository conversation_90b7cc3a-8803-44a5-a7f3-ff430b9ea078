package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceInvestmentInfo;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:54+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrTzqkToPoliceInvestmentInfoMapperImpl implements VWjBrTzqkToPoliceInvestmentInfoMapper {

    @Override
    public PoliceInvestmentInfo convert(VWjBrTzqk source) {
        if ( source == null ) {
            return null;
        }

        PoliceInvestmentInfo policeInvestmentInfo = new PoliceInvestmentInfo();

        policeInvestmentInfo.setInvestmentEntity( source.getTzcpmc() );
        policeInvestmentInfo.setInvestmentAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );
        policeInvestmentInfo.setIdCard( source.getGmsfhm() );
        policeInvestmentInfo.setInvestmentSource( source.getTzqx() );
        policeInvestmentInfo.setTransactionDate( ConversionUtils.strToDate( source.getTzsj() ) );

        return policeInvestmentInfo;
    }

    @Override
    public PoliceInvestmentInfo convert(VWjBrTzqk source, PoliceInvestmentInfo target) {
        if ( source == null ) {
            return target;
        }

        target.setInvestmentEntity( source.getTzcpmc() );
        target.setInvestmentAmount( ConversionUtils.strToBigDecimal( source.getJe() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setInvestmentSource( source.getTzqx() );
        target.setTransactionDate( ConversionUtils.strToDate( source.getTzsj() ) );

        return target;
    }
}
