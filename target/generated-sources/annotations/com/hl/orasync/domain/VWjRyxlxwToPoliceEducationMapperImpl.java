package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceEducation;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:54+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjRyxlxwToPoliceEducationMapperImpl implements VWjRyxlxwToPoliceEducationMapper {

    @Override
    public PoliceEducation convert(VWjRyxlxw source) {
        if ( source == null ) {
            return null;
        }

        PoliceEducation policeEducation = new PoliceEducation();

        policeEducation.setGraduationDate( ConversionUtils.strToDate( source.getBysj() ) );
        policeEducation.setEducationLevel( source.getXl() );
        policeEducation.setIdCard( source.getGmsfhm() );
        policeEducation.setEnrollmentDate( ConversionUtils.strToDate( source.getRxsj() ) );
        policeEducation.setMajorName( source.getZymc() );
        policeEducation.setSchoolName( source.getXxmc() );

        return policeEducation;
    }

    @Override
    public PoliceEducation convert(VWjRyxlxw source, PoliceEducation target) {
        if ( source == null ) {
            return target;
        }

        target.setGraduationDate( ConversionUtils.strToDate( source.getBysj() ) );
        target.setEducationLevel( source.getXl() );
        target.setIdCard( source.getGmsfhm() );
        target.setEnrollmentDate( ConversionUtils.strToDate( source.getRxsj() ) );
        target.setMajorName( source.getZymc() );
        target.setSchoolName( source.getXxmc() );

        return target;
    }
}
