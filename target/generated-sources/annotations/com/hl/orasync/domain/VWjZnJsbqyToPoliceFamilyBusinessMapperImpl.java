package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyBusiness;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:53+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjZnJsbqyToPoliceFamilyBusinessMapperImpl implements VWjZnJsbqyToPoliceFamilyBusinessMapper {

    @Override
    public PoliceFamilyBusiness convert(VWjZnJsbqy source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyBusiness policeFamilyBusiness = new PoliceFamilyBusiness();

        policeFamilyBusiness.setSeniorPositionDate( ConversionUtils.strToDate( source.getGjzwsj() ) );
        policeFamilyBusiness.setPersonalContribution( ConversionUtils.strToBigDecimal( source.getGrcze() ) );
        policeFamilyBusiness.setEstablishmentDate( ConversionUtils.strToDate( source.getClsj() ) );
        policeFamilyBusiness.setIdCard( source.getGmsfhm() );
        policeFamilyBusiness.setSeniorPositionName( source.getGjzwmc() );
        policeFamilyBusiness.setBusinessScope( source.getJyfw() );
        policeFamilyBusiness.setRegistrationAddress( source.getZcd() );
        policeFamilyBusiness.setSocialCreditCode( source.getZch() );
        policeFamilyBusiness.setInvestmentDate( ConversionUtils.strToDate( source.getTzsj() ) );
        policeFamilyBusiness.setEnterpriseType( source.getQylxmc() );
        policeFamilyBusiness.setRegisteredCapital( ConversionUtils.strToBigDecimal( source.getZczb() ) );
        if ( source.getSfgd() != null ) {
            policeFamilyBusiness.setIsShareholder( Byte.parseByte( source.getSfgd() ) );
        }
        policeFamilyBusiness.setEnterpriseStatus( source.getQyztmc() );
        policeFamilyBusiness.setName( source.getXmFr() );
        if ( source.getSfdrgjzw() != null ) {
            policeFamilyBusiness.setIsSeniorPosition( Byte.parseByte( source.getSfdrgjzw() ) );
        }
        policeFamilyBusiness.setBusinessAddress( source.getJyd() );
        policeFamilyBusiness.setEnterpriseName( source.getQymc() );
        policeFamilyBusiness.setPersonalContributionRatio( ConversionUtils.strToBigDecimal( source.getGrczbl() ) );

        return policeFamilyBusiness;
    }

    @Override
    public PoliceFamilyBusiness convert(VWjZnJsbqy source, PoliceFamilyBusiness target) {
        if ( source == null ) {
            return target;
        }

        target.setSeniorPositionDate( ConversionUtils.strToDate( source.getGjzwsj() ) );
        target.setPersonalContribution( ConversionUtils.strToBigDecimal( source.getGrcze() ) );
        target.setEstablishmentDate( ConversionUtils.strToDate( source.getClsj() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setSeniorPositionName( source.getGjzwmc() );
        target.setBusinessScope( source.getJyfw() );
        target.setRegistrationAddress( source.getZcd() );
        target.setSocialCreditCode( source.getZch() );
        target.setInvestmentDate( ConversionUtils.strToDate( source.getTzsj() ) );
        target.setEnterpriseType( source.getQylxmc() );
        target.setRegisteredCapital( ConversionUtils.strToBigDecimal( source.getZczb() ) );
        if ( source.getSfgd() != null ) {
            target.setIsShareholder( Byte.parseByte( source.getSfgd() ) );
        }
        else {
            target.setIsShareholder( null );
        }
        target.setEnterpriseStatus( source.getQyztmc() );
        target.setName( source.getXmFr() );
        if ( source.getSfdrgjzw() != null ) {
            target.setIsSeniorPosition( Byte.parseByte( source.getSfdrgjzw() ) );
        }
        else {
            target.setIsSeniorPosition( null );
        }
        target.setBusinessAddress( source.getJyd() );
        target.setEnterpriseName( source.getQymc() );
        target.setPersonalContributionRatio( ConversionUtils.strToBigDecimal( source.getGrczbl() ) );

        return target;
    }
}
