package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceViolationResult;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:54+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjWgwjdjbRycljgToPoliceViolationResultMapperImpl implements VWjWgwjdjbRycljgToPoliceViolationResultMapper {

    @Override
    public PoliceViolationResult convert(VWjWgwjdjbRycljg source) {
        if ( source == null ) {
            return null;
        }

        PoliceViolationResult policeViolationResult = new PoliceViolationResult();

        policeViolationResult.setCldw( source.getCldw() );
        policeViolationResult.setCljg( source.getCljg() );
        policeViolationResult.setRyXxzjbh( source.getRyXxzjbh() );
        policeViolationResult.setClsj( ConversionUtils.strToLocalDate( source.getClsj() ) );
        policeViolationResult.setLbmc( source.getLbmc() );
        policeViolationResult.setWtXxzjbh( source.getWtXxzjbh() );
        policeViolationResult.setXxzjbh( source.getXxzjbh() );

        return policeViolationResult;
    }

    @Override
    public PoliceViolationResult convert(VWjWgwjdjbRycljg source, PoliceViolationResult target) {
        if ( source == null ) {
            return target;
        }

        target.setCldw( source.getCldw() );
        target.setCljg( source.getCljg() );
        target.setRyXxzjbh( source.getRyXxzjbh() );
        target.setClsj( ConversionUtils.strToLocalDate( source.getClsj() ) );
        target.setLbmc( source.getLbmc() );
        target.setWtXxzjbh( source.getWtXxzjbh() );
        target.setXxzjbh( source.getXxzjbh() );

        return target;
    }
}
