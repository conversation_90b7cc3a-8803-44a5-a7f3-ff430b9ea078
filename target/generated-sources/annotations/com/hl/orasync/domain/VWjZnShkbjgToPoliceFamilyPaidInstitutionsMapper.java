package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyPaidInstitutions;
import com.hl.archive.domain.entity.PoliceFamilyPaidInstitutionsToVWjZnShkbjgMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__532;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__532.class,
    uses = {ConversionUtils.class,PoliceFamilyPaidInstitutionsToVWjZnShkbjgMapper.class},
    imports = {}
)
public interface VWjZnShkbjgToPoliceFamilyPaidInstitutionsMapper extends BaseMapper<VWjZnShkbjg, PoliceFamilyPaidInstitutions> {
  @Mapping(
      target = "registeredCapital",
      source = "zczb",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "establishmentDate",
      source = "clsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "institutionName",
      source = "qymc"
  )
  @Mapping(
      target = "name",
      source = "xmFr"
  )
  @Mapping(
      target = "businessScope",
      source = "jyfw"
  )
  @Mapping(
      target = "institutionType",
      source = "qylxmc"
  )
  @Mapping(
      target = "registrationAddress",
      source = "zcd"
  )
  @Mapping(
      target = "businessAddress",
      source = "jyd"
  )
  @Mapping(
      target = "socialCreditCode",
      source = "zch"
  )
  PoliceFamilyPaidInstitutions convert(VWjZnShkbjg source);

  @Mapping(
      target = "registeredCapital",
      source = "zczb",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "establishmentDate",
      source = "clsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "institutionName",
      source = "qymc"
  )
  @Mapping(
      target = "name",
      source = "xmFr"
  )
  @Mapping(
      target = "businessScope",
      source = "jyfw"
  )
  @Mapping(
      target = "institutionType",
      source = "qylxmc"
  )
  @Mapping(
      target = "registrationAddress",
      source = "zcd"
  )
  @Mapping(
      target = "businessAddress",
      source = "jyd"
  )
  @Mapping(
      target = "socialCreditCode",
      source = "zch"
  )
  PoliceFamilyPaidInstitutions convert(VWjZnShkbjg source,
      @MappingTarget PoliceFamilyPaidInstitutions target);
}
