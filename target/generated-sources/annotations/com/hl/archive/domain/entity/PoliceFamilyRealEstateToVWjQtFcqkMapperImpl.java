package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtFcqk;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:54+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyRealEstateToVWjQtFcqkMapperImpl implements PoliceFamilyRealEstateToVWjQtFcqkMapper {

    @Override
    public VWjQtFcqk convert(PoliceFamilyRealEstate source) {
        if ( source == null ) {
            return null;
        }

        VWjQtFcqk vWjQtFcqk = new VWjQtFcqk();

        vWjQtFcqk.setXmCqr( source.getPropertyOwnerName() );
        vWjQtFcqk.setCsjg( source.getSalePrice() );
        if ( source.getTransactionDate() != null ) {
            vWjQtFcqk.setJysj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTransactionDate() ) );
        }
        vWjQtFcqk.setFclymc( source.getPropertySource() );
        vWjQtFcqk.setGmsfhm( source.getIdCard() );
        vWjQtFcqk.setDz( source.getPropertyAddress() );
        if ( source.getSaleDate() != null ) {
            vWjQtFcqk.setCssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSaleDate() ) );
        }
        vWjQtFcqk.setFclxmc( source.getPropertyType() );
        vWjQtFcqk.setFcqxmc( source.getPropertyDisposition() );
        vWjQtFcqk.setJyjg( source.getTransactionPrice() );
        vWjQtFcqk.setJzmj( source.getBuildingArea() );

        return vWjQtFcqk;
    }

    @Override
    public VWjQtFcqk convert(PoliceFamilyRealEstate source, VWjQtFcqk target) {
        if ( source == null ) {
            return target;
        }

        target.setXmCqr( source.getPropertyOwnerName() );
        target.setCsjg( source.getSalePrice() );
        if ( source.getTransactionDate() != null ) {
            target.setJysj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getTransactionDate() ) );
        }
        else {
            target.setJysj( null );
        }
        target.setFclymc( source.getPropertySource() );
        target.setGmsfhm( source.getIdCard() );
        target.setDz( source.getPropertyAddress() );
        if ( source.getSaleDate() != null ) {
            target.setCssj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSaleDate() ) );
        }
        else {
            target.setCssj( null );
        }
        target.setFclxmc( source.getPropertyType() );
        target.setFcqxmc( source.getPropertyDisposition() );
        target.setJyjg( source.getTransactionPrice() );
        target.setJzmj( source.getBuildingArea() );

        return target;
    }
}
