package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnJsbqy;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:54+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyBusinessToVWjZnJsbqyMapperImpl implements PoliceFamilyBusinessToVWjZnJsbqyMapper {

    @Override
    public VWjZnJsbqy convert(PoliceFamilyBusiness source) {
        if ( source == null ) {
            return null;
        }

        VWjZnJsbqy vWjZnJsbqy = new VWjZnJsbqy();

        if ( source.getPersonalContribution() != null ) {
            vWjZnJsbqy.setGrcze( source.getPersonalContribution().toString() );
        }
        vWjZnJsbqy.setGjzwmc( source.getSeniorPositionName() );
        if ( source.getIsShareholder() != null ) {
            vWjZnJsbqy.setSfgd( String.valueOf( source.getIsShareholder() ) );
        }
        vWjZnJsbqy.setQylxmc( source.getEnterpriseType() );
        if ( source.getSeniorPositionDate() != null ) {
            vWjZnJsbqy.setGjzwsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSeniorPositionDate() ) );
        }
        vWjZnJsbqy.setGmsfhm( source.getIdCard() );
        if ( source.getRegisteredCapital() != null ) {
            vWjZnJsbqy.setZczb( source.getRegisteredCapital().toString() );
        }
        vWjZnJsbqy.setQymc( source.getEnterpriseName() );
        if ( source.getInvestmentDate() != null ) {
            vWjZnJsbqy.setTzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getInvestmentDate() ) );
        }
        vWjZnJsbqy.setQyztmc( source.getEnterpriseStatus() );
        if ( source.getEstablishmentDate() != null ) {
            vWjZnJsbqy.setClsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEstablishmentDate() ) );
        }
        vWjZnJsbqy.setJyfw( source.getBusinessScope() );
        if ( source.getPersonalContributionRatio() != null ) {
            vWjZnJsbqy.setGrczbl( source.getPersonalContributionRatio().toString() );
        }
        vWjZnJsbqy.setJyd( source.getBusinessAddress() );
        vWjZnJsbqy.setXmFr( source.getName() );
        vWjZnJsbqy.setZcd( source.getRegistrationAddress() );
        vWjZnJsbqy.setZch( source.getSocialCreditCode() );
        if ( source.getIsSeniorPosition() != null ) {
            vWjZnJsbqy.setSfdrgjzw( String.valueOf( source.getIsSeniorPosition() ) );
        }

        return vWjZnJsbqy;
    }

    @Override
    public VWjZnJsbqy convert(PoliceFamilyBusiness source, VWjZnJsbqy target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getPersonalContribution() != null ) {
            target.setGrcze( source.getPersonalContribution().toString() );
        }
        else {
            target.setGrcze( null );
        }
        target.setGjzwmc( source.getSeniorPositionName() );
        if ( source.getIsShareholder() != null ) {
            target.setSfgd( String.valueOf( source.getIsShareholder() ) );
        }
        else {
            target.setSfgd( null );
        }
        target.setQylxmc( source.getEnterpriseType() );
        if ( source.getSeniorPositionDate() != null ) {
            target.setGjzwsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSeniorPositionDate() ) );
        }
        else {
            target.setGjzwsj( null );
        }
        target.setGmsfhm( source.getIdCard() );
        if ( source.getRegisteredCapital() != null ) {
            target.setZczb( source.getRegisteredCapital().toString() );
        }
        else {
            target.setZczb( null );
        }
        target.setQymc( source.getEnterpriseName() );
        if ( source.getInvestmentDate() != null ) {
            target.setTzsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getInvestmentDate() ) );
        }
        else {
            target.setTzsj( null );
        }
        target.setQyztmc( source.getEnterpriseStatus() );
        if ( source.getEstablishmentDate() != null ) {
            target.setClsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEstablishmentDate() ) );
        }
        else {
            target.setClsj( null );
        }
        target.setJyfw( source.getBusinessScope() );
        if ( source.getPersonalContributionRatio() != null ) {
            target.setGrczbl( source.getPersonalContributionRatio().toString() );
        }
        else {
            target.setGrczbl( null );
        }
        target.setJyd( source.getBusinessAddress() );
        target.setXmFr( source.getName() );
        target.setZcd( source.getRegistrationAddress() );
        target.setZch( source.getSocialCreditCode() );
        if ( source.getIsSeniorPosition() != null ) {
            target.setSfdrgjzw( String.valueOf( source.getIsSeniorPosition() ) );
        }
        else {
            target.setSfdrgjzw( null );
        }

        return target;
    }
}
