package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjXhjhRxgr;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:54+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceProjectEntryPersonToVWjXhjhRxgrMapperImpl implements PoliceProjectEntryPersonToVWjXhjhRxgrMapper {

    @Override
    public VWjXhjhRxgr convert(PoliceProjectEntryPerson source) {
        if ( source == null ) {
            return null;
        }

        VWjXhjhRxgr vWjXhjhRxgr = new VWjXhjhRxgr();

        if ( source.getEntryTime() != null ) {
            vWjXhjhRxgr.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEntryTime() ) );
        }
        vWjXhjhRxgr.setShztmc( source.getAuditStatus() );
        vWjXhjhRxgr.setXm( source.getName() );
        vWjXhjhRxgr.setGmsfhm( source.getIdCard() );
        vWjXhjhRxgr.setRyjbmc( source.getHonorLevel() );
        vWjXhjhRxgr.setDwmc( source.getUnit() );
        vWjXhjhRxgr.setZgrymc( source.getHonorName() );
        vWjXhjhRxgr.setPylxrXm( source.getContactPerson() );
        vWjXhjhRxgr.setJh( source.getPoliceNumber() );
        vWjXhjhRxgr.setXxzjbh( source.getZjbh() );

        return vWjXhjhRxgr;
    }

    @Override
    public VWjXhjhRxgr convert(PoliceProjectEntryPerson source, VWjXhjhRxgr target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getEntryTime() != null ) {
            target.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEntryTime() ) );
        }
        else {
            target.setDjsj( null );
        }
        target.setShztmc( source.getAuditStatus() );
        target.setXm( source.getName() );
        target.setGmsfhm( source.getIdCard() );
        target.setRyjbmc( source.getHonorLevel() );
        target.setDwmc( source.getUnit() );
        target.setZgrymc( source.getHonorName() );
        target.setPylxrXm( source.getContactPerson() );
        target.setJh( source.getPoliceNumber() );
        target.setXxzjbh( source.getZjbh() );

        return target;
    }
}
