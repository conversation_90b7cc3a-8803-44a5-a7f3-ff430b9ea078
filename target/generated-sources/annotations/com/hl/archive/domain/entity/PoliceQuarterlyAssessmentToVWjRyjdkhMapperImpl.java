package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjdkh;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-15T15:32:54+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceQuarterlyAssessmentToVWjRyjdkhMapperImpl implements PoliceQuarterlyAssessmentToVWjRyjdkhMapper {

    @Override
    public VWjRyjdkh convert(PoliceQuarterlyAssessment source) {
        if ( source == null ) {
            return null;
        }

        VWjRyjdkh vWjRyjdkh = new VWjRyjdkh();

        vWjRyjdkh.setJgmc( source.getAssessmentResult() );
        vWjRyjdkh.setNd( source.getAssessmentYear() );
        vWjRyjdkh.setDwmc( source.getAssessmentUnit() );
        vWjRyjdkh.setGmsfhm( source.getIdCard() );
        vWjRyjdkh.setJd( source.getAssessmentQuarter() );

        return vWjRyjdkh;
    }

    @Override
    public VWjRyjdkh convert(PoliceQuarterlyAssessment source, VWjRyjdkh target) {
        if ( source == null ) {
            return target;
        }

        target.setJgmc( source.getAssessmentResult() );
        target.setNd( source.getAssessmentYear() );
        target.setDwmc( source.getAssessmentUnit() );
        target.setGmsfhm( source.getIdCard() );
        target.setJd( source.getAssessmentQuarter() );

        return target;
    }
}
