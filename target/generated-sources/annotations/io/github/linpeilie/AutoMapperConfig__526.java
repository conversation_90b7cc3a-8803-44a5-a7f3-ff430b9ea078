package io.github.linpeilie;

import com.hl.orasync.convert.PoliceBasicInfoConvert;
import com.hl.orasync.convert.PolicePositionRankConvert;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__526.class, PolicePositionRankConvert.class, PoliceBasicInfoConvert.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__526 {
}
